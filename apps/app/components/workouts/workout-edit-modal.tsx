'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { updateWorkout, useWorkoutsList } from '@/hooks/use-workouts';
import { hasWorkoutTimeConflict } from '@/lib/workout-utils';
import type { WorkoutResponse, UpdateWorkoutInput } from '@/lib/validations';

interface WorkoutEditModalProps {
  workout: WorkoutResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function WorkoutEditModal({ workout, open, onOpenChange, onSuccess }: WorkoutEditModalProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [startTime, setStartTime] = useState('10:00');
  const [endTime, setEndTime] = useState('11:00');
  const [location, setLocation] = useState('');
  const [minParticipants, setMinParticipants] = useState(3);
  const [maxParticipants, setMaxParticipants] = useState(5);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);

  // Get all workouts for conflict checking
  const { workouts: allWorkouts } = useWorkoutsList({ limit: 1000 });

  // Reset form when workout changes
  useEffect(() => {
    if (workout) {
      setTitle(workout.title);
      setDescription(workout.description || '');

      const start = new Date(workout.startTime);
      const end = new Date(workout.endTime);

      setStartDate(start);
      setEndDate(end);
      setStartTime(formatTimeForInput(start));
      setEndTime(formatTimeForInput(end));
      setLocation(workout.location || '');
      setMinParticipants(workout.minParticipants);
      setMaxParticipants(workout.maxParticipants);
      setError(null);
    }
  }, [workout]);

  const formatTimeForInput = (date: Date): string => {
    return date.toTimeString().slice(0, 5); // HH:MM format
  };

  const parseTimeInput = (timeStr: string, date: Date): Date => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const newDate = new Date(date);
    if (!hours || !minutes) {
      return newDate;
    }

    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const validateForm = (): string | null => {
    if (!title.trim()) {
      return 'Title is required';
    }

    if (title.length > 100) {
      return 'Title must be less than 100 characters';
    }

    if (minParticipants < 1 || minParticipants > 10) {
      return 'Minimum participants must be between 1 and 10';
    }

    if (maxParticipants < 1 || maxParticipants > 10) {
      return 'Maximum participants must be between 1 and 10';
    }

    if (minParticipants > maxParticipants) {
      return 'Minimum participants cannot exceed maximum participants';
    }

    // Validate 1-hour duration
    const start = parseTimeInput(startTime, startDate);
    const end = parseTimeInput(endTime, endDate);
    const durationMs = end.getTime() - start.getTime();
    const durationHours = durationMs / (1000 * 60 * 60);

    if (durationHours !== 1) {
      return 'Workout sessions must be exactly 1 hour long';
    }

    if (start >= end) {
      return 'End time must be after start time';
    }

    return null;
  };

  const checkTimeConflicts = (start: Date, end: Date): string | null => {
    if (!workout) return null;

    const { hasConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
      start,
      end,
      allWorkouts,
      workout.id // Exclude current workout
    );

    if (hasConflict && conflictingWorkouts.length > 0) {
      const conflictingWorkout = conflictingWorkouts[0];
      if (!conflictingWorkout) {
        return null;
      }

      const conflictTime = new Date(conflictingWorkout.startTime).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
      return `Time slot conflicts with existing "${conflictingWorkout.title}" session at ${conflictTime}`;
    }

    return null;
  };

  const handleSubmit = async () => {
    if (!workout) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    const start = parseTimeInput(startTime, startDate);
    const end = parseTimeInput(endTime, endDate);

    const conflictError = checkTimeConflicts(start, end);
    if (conflictError) {
      setError(conflictError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const updateData: UpdateWorkoutInput = {
        title: title.trim(),
        description: description.trim() || undefined,
        startTime: start.toISOString(),
        endTime: end.toISOString(),
        location: location.trim() || undefined,
        minParticipants,
        maxParticipants,
      };

      await updateWorkout(workout.id, updateData);
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to update workout:', error);
      setError(error instanceof Error ? error.message : 'Failed to update workout');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setError(null);
  };

  if (!workout) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Workout</DialogTitle>
          <DialogDescription>Update the workout details. All changes will be saved immediately.</DialogDescription>
        </DialogHeader>

        {error && <div className="bg-destructive/15 text-destructive rounded-md px-3 py-2 text-sm">{error}</div>}

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Workout title"
              maxLength={100}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Workout description (optional)"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>Start Date</Label>
              <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn('justify-start text-left font-normal', !startDate && 'text-muted-foreground')}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      if (date) {
                        setStartDate(date);
                        setEndDate(date); // Keep end date same as start date
                      }
                      setStartDateOpen(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="start-time">Start Time</Label>
              <Input
                id="start-time"
                type="time"
                value={startTime}
                onChange={(e) => {
                  setStartTime(e.target.value);
                  // Auto-update end time to maintain 1-hour duration
                  const [hours, minutes] = e.target.value.split(':').map(Number);
                  if (!hours || !minutes) {
                    return;
                  }

                  const endHour = hours + 1;
                  setEndTime(`${endHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`);
                }}
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="Workout location (optional)"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="min-participants">Min Participants</Label>
              <Input
                id="min-participants"
                type="number"
                min="1"
                max="10"
                value={minParticipants}
                onChange={(e) => setMinParticipants(parseInt(e.target.value) || 1)}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="max-participants">Max Participants</Label>
              <Input
                id="max-participants"
                type="number"
                min="1"
                max="10"
                value={maxParticipants}
                onChange={(e) => setMaxParticipants(parseInt(e.target.value) || 1)}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
