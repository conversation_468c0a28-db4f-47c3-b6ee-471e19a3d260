{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "ui-experiments", "homepage": "https://originui.com/layouts", "items": [{"name": "experiment-06", "type": "registry:style", "cssVars": {"theme": {"font-sans": "var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-mono": "var(--font-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"}, "light": {"background": "oklch(0.985 0 0)", "foreground": "oklch(0.141 0.005 285.823)", "card": "oklch(0.985 0 0)", "card-foreground": "oklch(0.141 0.005 285.823)", "popover": "oklch(0.985 0 0)", "popover-foreground": "oklch(0.141 0.005 285.823)", "primary": "oklch(0.21 0.006 285.885)", "primary-foreground": "oklch(0.985 0 0)", "secondary": "oklch(0.967 0.001 286.375)", "secondary-foreground": "oklch(0.21 0.006 285.885)", "muted": "oklch(0.967 0.001 286.375)", "muted-foreground": "oklch(0.552 0.016 285.938)", "accent": "oklch(0.967 0.001 286.375)", "accent-foreground": "oklch(0.21 0.006 285.885)", "destructive": "oklch(0.577 0.245 27.325)", "destructive-foreground": "oklch(0.967 0.001 286.375)", "border": "oklch(0.92 0.004 286.32)", "input": "oklch(0.92 0.004 286.32)", "ring": "oklch(0.705 0.015 286.067)", "chart-1": "oklch(0.646 0.222 41.116)", "chart-2": "oklch(0.6 0.118 184.704)", "chart-3": "oklch(0.398 0.07 227.392)", "chart-4": "oklch(0.828 0.189 84.429)", "chart-5": "oklch(0.769 0.188 70.08)", "radius": "0.625rem", "sidebar": "oklch(0.21 0.006 285.885)", "sidebar-foreground": "oklch(0.92 0.004 286.32)", "sidebar-primary": "oklch(0.21 0.006 285.885)", "sidebar-primary-foreground": "oklch(0.21 0.006 285.885)", "sidebar-accent": "oklch(0.92 0.004 286.32)", "sidebar-accent-foreground": "oklch(0.21 0.006 285.885)", "sidebar-border": "oklch(0.92 0.004 286.32)", "sidebar-ring": "oklch(0.705 0.015 286.067)"}, "dark": {"background": "oklch(0.256 0.006 286.033)", "foreground": "oklch(0.985 0 0)", "card": "oklch(0.21 0.006 285.885)", "card-foreground": "oklch(0.985 0 0)", "popover": "oklch(0.256 0.006 286.033)", "popover-foreground": "oklch(0.985 0 0)", "primary": "oklch(0.92 0.004 286.32)", "primary-foreground": "oklch(0.21 0.006 285.885)", "secondary": "oklch(0.37 0.013 285.805)", "secondary-foreground": "oklch(0.985 0 0)", "muted": "oklch(0.37 0.013 285.805)", "muted-foreground": "oklch(0.705 0.015 286.067)", "accent": "oklch(0.37 0.013 285.805)", "accent-foreground": "oklch(0.985 0 0)", "destructive": "oklch(0.704 0.191 22.216)", "destructive-foreground": "oklch(0.985 0 0)", "border": "oklch(0.985 0 0 / 10%)", "input": "oklch(0.985 0 0 / 15%)", "ring": "oklch(0.552 0.016 285.938)", "chart-1": "oklch(0.488 0.243 264.376)", "chart-2": "oklch(0.696 0.17 162.48)", "chart-3": "oklch(0.769 0.188 70.08)", "chart-4": "oklch(0.627 0.265 303.9)", "chart-5": "oklch(0.645 0.246 16.439)", "radius": "0.625rem", "sidebar": "oklch(0.21 0.006 285.885)", "sidebar-foreground": "oklch(0.967 0.001 286.375)", "sidebar-primary": "oklch(0.488 0.243 264.376)", "sidebar-primary-foreground": "oklch(0.985 0 0)", "sidebar-accent": "oklch(0.256 0.006 286.033)", "sidebar-accent-foreground": "oklch(0.985 0 0)", "sidebar-border": "oklch(0.985 0 0 / 10%)", "sidebar-ring": "oklch(0.442 0.017 285.786)"}}, "dependencies": ["@dnd-kit/core", "@dnd-kit/modifiers", "@dnd-kit/utilities", "@radix-ui/react-avatar", "@radix-ui/react-checkbox", "@radix-ui/react-collapsible", "@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu", "@radix-ui/react-label", "@radix-ui/react-popover", "@radix-ui/react-radio-group", "@radix-ui/react-select", "@radix-ui/react-separator", "@radix-ui/react-slot", "@radix-ui/react-tooltip", "@remixicon/react", "date-fns", "next-themes", "react-day-picker", "sonner"], "devDependencies": ["tw-animate-css"], "files": [{"path": "app/layout.tsx", "type": "registry:page", "target": "app/layout.tsx"}, {"path": "app/page.tsx", "type": "registry:page", "target": "app/dashboard/page.tsx"}, {"path": "components/app-sidebar.tsx", "type": "registry:component"}, {"path": "components/big-calendar.tsx", "type": "registry:component"}, {"path": "components/nav-user.tsx", "type": "registry:component"}, {"path": "components/participants.tsx", "type": "registry:component"}, {"path": "components/sidebar-calendar.tsx", "type": "registry:component"}, {"path": "components/theme-toggle.tsx", "type": "registry:component"}, {"path": "components/ui/avatar.tsx", "type": "registry:component"}, {"path": "components/ui/button.tsx", "type": "registry:component"}, {"path": "components/ui/calendar.tsx", "type": "registry:component"}, {"path": "components/ui/checkbox.tsx", "type": "registry:component"}, {"path": "components/ui/collapsible.tsx", "type": "registry:component"}, {"path": "components/ui/dialog.tsx", "type": "registry:component"}, {"path": "components/ui/dropdown-menu.tsx", "type": "registry:component"}, {"path": "components/ui/input.tsx", "type": "registry:component"}, {"path": "components/ui/label.tsx", "type": "registry:component"}, {"path": "components/ui/popover.tsx", "type": "registry:component"}, {"path": "components/ui/radio-group.tsx", "type": "registry:component"}, {"path": "components/ui/select.tsx", "type": "registry:component"}, {"path": "components/ui/separator.tsx", "type": "registry:component"}, {"path": "components/ui/sheet.tsx", "type": "registry:component"}, {"path": "components/ui/sidebar.tsx", "type": "registry:component"}, {"path": "components/ui/skeleton.tsx", "type": "registry:component"}, {"path": "components/ui/sonner.tsx", "type": "registry:component"}, {"path": "components/ui/textarea.tsx", "type": "registry:component"}, {"path": "components/ui/tooltip.tsx", "type": "registry:component"}, {"path": "components/event-calendar/agenda-view.tsx", "type": "registry:component"}, {"path": "components/event-calendar/calendar-context.tsx", "type": "registry:component"}, {"path": "components/event-calendar/calendar-dnd-context.tsx", "type": "registry:component"}, {"path": "components/event-calendar/constants.ts", "type": "registry:component"}, {"path": "components/event-calendar/day-view.tsx", "type": "registry:component"}, {"path": "components/event-calendar/draggable-event.tsx", "type": "registry:component"}, {"path": "components/event-calendar/droppable-cell.tsx", "type": "registry:component"}, {"path": "components/event-calendar/event-dialog.tsx", "type": "registry:component"}, {"path": "components/event-calendar/event-item.tsx", "type": "registry:component"}, {"path": "components/event-calendar/events-popup.tsx", "type": "registry:component"}, {"path": "components/event-calendar/event-calendar.tsx", "type": "registry:component"}, {"path": "components/event-calendar/index.ts", "type": "registry:component"}, {"path": "components/event-calendar/month-view.tsx", "type": "registry:component"}, {"path": "components/event-calendar/types.ts", "type": "registry:component"}, {"path": "components/event-calendar/utils.ts", "type": "registry:component"}, {"path": "components/event-calendar/week-view.tsx", "type": "registry:component"}, {"path": "components/event-calendar/hooks/use-current-time-indicator.ts", "type": "registry:component"}, {"path": "components/event-calendar/hooks/use-event-visibility.ts", "type": "registry:component"}, {"path": "hooks/use-mobile.ts", "type": "registry:hook"}, {"path": "providers/theme-provider.tsx", "type": "registry:file", "target": "providers/theme-provider.tsx"}, {"path": "next.config.mjs", "type": "registry:file", "target": "next.config.mjs"}]}]}